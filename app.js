const express = require('express');
const axios = require('axios');
const cheerio = require('cheerio');
const path = require('path');
const ItemCache = require('./cache');

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize cache
const itemCache = new ItemCache();

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// Parse JSON and URL-encoded data
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Route for the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Function to map our form fields to PQDI field names
function mapFormDataToPQDI(formData) {
    try {
        console.log('Mapping form data:', formData);
        // Start with the exact default values from the actual PQDI request
        const pqdiData = {
        item_name: formData.itemName || '',
        class_select: 'Class',
        race_select: 'Race',
        slot_select: 'Slot',
        type_select: 'Type',
        stat1_select: 'Stat',
        sel1_select: '>',
        stat1_int: '0',
        stat2_select: 'Stat',
        sel2_select: '>',
        stat2_int: '0',
        stat3_select: 'Stat',
        sel3_select: '>',
        stat3_int: '0',
        stat4_select: 'Stat',
        sel4_select: '>',
        stat4_int: '0',
        resist_select: 'Resist',
        res_select: '>',
        res_int: '0',
        skills_select: 'Skill Mod',
        sk_select: '>',
        sk_int: '0',
        // Note: has_proc, has_click, has_focus, has_worn, is_droppable are only added if checkboxes are checked
        // Not including them means "include all items" rather than "exclude items with these properties"
        effect_name: formData.effectName || '',
        bag_select: 'Container',
        bagslots: '0',
        wr: '0',
        submit: 'Search'  // This was missing!
    };

    // Class mapping (name to numeric value)
    const classMap = {
        'Warrior': '1', 'Cleric': '2', 'Paladin': '4', 'Ranger': '8',
        'Shadow Knight': '16', 'Druid': '32', 'Monk': '64', 'Bard': '128',
        'Rogue': '256', 'Shaman': '512', 'Necromancer': '1024', 'Wizard': '2048',
        'Magician': '4096', 'Enchanter': '8192', 'Beastlord': '16384'
    };
    if (formData.class && classMap[formData.class]) {
        pqdiData.class_select = classMap[formData.class];
    }

    // Race mapping
    const raceMap = {
        'Human': '1', 'Barbarian': '2', 'Erudite': '4', 'Wood Elf': '8',
        'High Elf': '16', 'Dark Elf': '32', 'Half Elf': '64', 'Dwarf': '128',
        'Troll': '256', 'Ogre': '512', 'Halfling': '1024', 'Gnome': '2048',
        'Iksar': '4096', 'Vah Shir': '8192'
    };
    if (formData.race && raceMap[formData.race]) {
        pqdiData.race_select = raceMap[formData.race];
    }

    // Slot mapping
    const slotMap = {
        'Charm': '1', 'Ear': '2', 'Head': '4', 'Face': '8', 'Ears': '18',
        'Neck': '32', 'Shoulders': '64', 'Arms': '128', 'Back': '256',
        'Wrists': '1536', 'Range': '2048', 'Hands': '4096', 'Primary': '8192',
        'Secondary': '16384', 'Fingers': '98304', 'Chest': '131072',
        'Legs': '262144', 'Feet': '524288', 'Waist': '1048576', 'Ammo': '2097152'
    };
    if (formData.slot && slotMap[formData.slot]) {
        pqdiData.slot_select = slotMap[formData.slot];
    }

    // Item Type mapping
    const typeMap = {
        '1HS': '0', '2HS': '1', 'Piercing': '2', '1HB': '3', '2HB': '4',
        'Archery': '5', 'Throwing range items': '7', 'Shield': '8', 'Armor': '10',
        'Gems': '11', 'Lockpicks': '12', 'Food': '14', 'Drink': '15', 'Light': '16',
        'Combinable': '17', 'Bandages': '18', 'Throwing': '19', 'Scroll': '20',
        'Potion': '21', 'Wind Instrument': '23', 'Stringed Instrument': '24',
        'Brass Instrument': '25', 'Percussion Instrument': '26', 'Arrow': '27',
        'Jewelry': '29', 'Skull': '30', 'Tome': '31', 'Note': '32', 'Key': '33',
        'Coin': '34', '2H Piercing': '35', 'Fishing Pole': '36', 'Fishing Bait': '37',
        'Alcohol': '38', 'Key (bis)': '39', 'Compass': '40', 'Poison': '42',
        'Martial': '45', 'Charm': '52', 'Augmentation': '54'
    };
    if (formData.itemType && typeMap[formData.itemType]) {
        pqdiData.type_select = typeMap[formData.itemType];
    }

    // Stats mapping
    const statMap = {
        'Hit Points': 'hp', 'Mana': 'mana', 'AC': 'ac', 'Attack': 'attack',
        'Agility': 'aagi', 'Charisma': 'acha', 'Dexterity': 'adex',
        'Intelligence': 'aint', 'Stamina': 'asta', 'Strength': 'astr',
        'Wisdom': 'awis', 'Damage': 'damage', 'Delay': 'delay'
    };

    if (formData.stat1 && statMap[formData.stat1]) {
        pqdiData.stat1_select = statMap[formData.stat1];
        pqdiData.sel1_select = formData.stat1Operator || '>';
        pqdiData.stat1_int = formData.stat1Value || '0';
    }

    if (formData.stat2 && statMap[formData.stat2]) {
        pqdiData.stat2_select = statMap[formData.stat2];
        pqdiData.sel2_select = formData.stat2Operator || '>';
        pqdiData.stat2_int = formData.stat2Value || '0';
    }

    if (formData.stat3 && statMap[formData.stat3]) {
        pqdiData.stat3_select = statMap[formData.stat3];
        pqdiData.sel3_select = formData.stat3Operator || '>';
        pqdiData.stat3_int = formData.stat3Value || '0';
    }

    if (formData.stat4 && statMap[formData.stat4]) {
        pqdiData.stat4_select = statMap[formData.stat4];
        pqdiData.sel4_select = formData.stat4Operator || '>';
        pqdiData.stat4_int = formData.stat4Value || '0';
    }

    // Resist mapping
    const resistMap = {
        'Resist Magic': 'mr', 'Resist Fire': 'fr', 'Resist Cold': 'cr',
        'Resist Poison': 'pr', 'Resist Disease': 'dr'
    };
    if (formData.resist && resistMap[formData.resist]) {
        pqdiData.resist_select = resistMap[formData.resist];
        pqdiData.res_select = formData.resistOperator || '>';
        pqdiData.res_int = formData.resistValue || '0';
    }

    // Skills mapping
    const skillMap = {
        '1H Blunt': '0', '1H Slashing': '1', '2H Blunt': '2', '2H Slashing': '3',
        'Abjuration': '4', 'Alteration': '5', 'Apply Poison': '6', 'Archery': '7',
        'Backstab': '8', 'Bind Wound': '9', 'Bash': '10', 'Block': '11',
        'Brass Instruments': '12', 'Channeling': '13', 'Conjuration': '14',
        'Defense': '15', 'Disarm': '16', 'Disarm Traps': '17', 'Divination': '18',
        'Dodge': '19', 'Double Attack': '20', 'Dragon Punch': '21', 'Dual Wield': '22',
        'Eagle Strike': '23', 'Evocation': '24', 'Feign Death': '25', 'Flying Kick': '26',
        'Forage': '27', 'Hand to Hand': '28', 'Hide': '29', 'Intimidation': '30',
        'Kick': '31', 'Meditate': '32', 'Mend': '33', 'Offense': '34',
        'Parry': '35', 'Pick Lock': '36', 'Piercing': '37', 'Riposte': '38',
        'Round Kick': '39', 'Safe Fall': '40', 'Sense Heading': '41', 'Singing': '42',
        'Sneak': '43', 'Stringed Instruments': '44', 'Swimming': '45', 'Taunt': '46',
        'Throwing': '47', 'Tiger Claw': '48', 'Track': '49', 'Wind Instruments': '50'
    };
    if (formData.skill && skillMap[formData.skill]) {
        pqdiData.skills_select = skillMap[formData.skill];
        pqdiData.sk_select = formData.skillOperator || '>';
        pqdiData.sk_int = formData.skillValue || '0';
    }

    // Checkboxes
    if (formData.proc) pqdiData.has_proc = 'y';
    if (formData.click) pqdiData.has_click = 'y';
    if (formData.focus) pqdiData.has_focus = 'y';
    if (formData.worn) pqdiData.has_worn = 'y';
    if (formData.droppable) pqdiData.is_droppable = 'y';
    if (formData.legacyItems) pqdiData.legacy = 'y';
    if (formData.tableView) pqdiData.table_view = 'y';

    // Container mapping
    const containerMap = {
        'Just a Bag': '1', 'Quiver': '2', 'Pouch': '3', 'Backpack': '5',
        'Tupperware': '6', 'Box': '7', 'Bandolier': '8', 'Alchemy': '9',
        'Tinkering': '10', 'Research': '11', 'Poison making': '12',
        'Special quests': '13', 'Baking: Mixing': '14', 'Baking: Cooking': '15',
        'Tailoring: Sewing Kit': '16', 'Fletching': '18', 'Brewing': '19',
        'Jewelry': '20', 'Wizard Research': '24', 'Mage Research': '25',
        'Necro Research': '26', 'Enchanter Research': '27', 'Plat Storage': '28',
        'Practice Research': '29', 'Pottery': '30', 'Tailoring: Vale': '41',
        'Tailoring: Erudite': '42', 'Tailoring: Fier\'Dal': '43', 'Fishing': '46',
        'Bazaar': '51'
    };
    if (formData.container && containerMap[formData.container]) {
        pqdiData.bag_select = containerMap[formData.container];
    }

    if (formData.bagSlots) pqdiData.bagslots = formData.bagSlots;
    if (formData.bagWeightReduction) pqdiData.wr = formData.bagWeightReduction;

    console.log('Final mapped PQDI data:', pqdiData);
    return pqdiData;
    } catch (error) {
        console.error('Error in mapFormDataToPQDI:', error);
        throw error;
    }
}

// Route to handle search requests
app.post('/search', async (req, res) => {
    try {
        console.log('Original search parameters:', req.body);

        // Map our form data to PQDI format
        const pqdiData = mapFormDataToPQDI(req.body);
        console.log('Mapped PQDI parameters:', pqdiData);

        // First, get the CSRF token from the PQDI items page
        console.log('Fetching CSRF token from PQDI...');
        const itemsPageResponse = await axios.get('https://www.pqdi.cc/items');
        console.log('PQDI page response status:', itemsPageResponse.status);

        // Extract both CSRF token and session cookie
        const $ = cheerio.load(itemsPageResponse.data);
        const csrfToken = $('input[name="csrf_token"]').val();
        const sessionCookie = itemsPageResponse.headers['set-cookie']?.find(cookie => cookie.startsWith('session='));

        console.log('CSRF token found:', csrfToken ? 'Yes' : 'No');
        console.log('CSRF token value:', csrfToken);
        console.log('Session cookie found:', sessionCookie ? 'Yes' : 'No');

        if (!csrfToken) {
            throw new Error('Could not retrieve CSRF token');
        }

        // Add CSRF token to our data
        pqdiData.csrf_token = csrfToken;

        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Make the search request to PQDI using the exact same format as working request
        console.log('Making POST request to PQDI with data:', { csrf_token: pqdiData.csrf_token, item_name: pqdiData.item_name });

        const searchResponse = await axios.post('https://www.pqdi.cc/items', pqdiData, {
            headers: {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'en-US,en;q=0.9',
                'cache-control': 'max-age=0',
                'content-type': 'application/x-www-form-urlencoded',
                'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'same-origin',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1',
                'cookie': sessionCookie || '',
                'Referer': 'https://www.pqdi.cc/items'
            },
            transformRequest: [(data) => {
                // Create exact body format as working request
                const params = new URLSearchParams();
                params.append('csrf_token', data.csrf_token);
                params.append('item_name', data.item_name);
                params.append('class_select', data.class_select);
                params.append('race_select', data.race_select);
                params.append('slot_select', data.slot_select);
                params.append('type_select', data.type_select);
                params.append('stat1_select', data.stat1_select);
                params.append('sel1_select', data.sel1_select);
                params.append('stat1_int', data.stat1_int);
                params.append('stat2_select', data.stat2_select);
                params.append('sel2_select', data.sel2_select);
                params.append('stat2_int', data.stat2_int);
                params.append('stat3_select', data.stat3_select);
                params.append('sel3_select', data.sel3_select);
                params.append('stat3_int', data.stat3_int);
                params.append('stat4_select', data.stat4_select);
                params.append('sel4_select', data.sel4_select);
                params.append('stat4_int', data.stat4_int);
                params.append('resist_select', data.resist_select);
                params.append('res_select', data.res_select);
                params.append('res_int', data.res_int);
                params.append('skills_select', data.skills_select);
                params.append('sk_select', data.sk_select);
                params.append('sk_int', data.sk_int);

                // Only include checkbox parameters if they are set to 'y'
                // Not including them means "include all items" rather than "exclude items with these properties"
                if (data.has_proc === 'y') params.append('has_proc', 'y');
                if (data.has_click === 'y') params.append('has_click', 'y');
                if (data.has_focus === 'y') params.append('has_focus', 'y');
                if (data.has_worn === 'y') params.append('has_worn', 'y');
                if (data.is_droppable === 'y') params.append('is_droppable', 'y');

                params.append('effect_name', data.effect_name);
                params.append('bag_select', data.bag_select);
                params.append('bagslots', data.bagslots);
                params.append('wr', data.wr);
                params.append('submit', data.submit);
                return params.toString();
            }]
        });

        // Parse the response HTML to extract search results
        const results = parseSearchResults(searchResponse.data);

        res.json({
            success: true,
            message: `Found ${results.items.length} items`,
            searchParams: req.body,
            pqdiParams: pqdiData,
            results: results
        });

    } catch (error) {
        console.error('Search error:', error.message);
        console.error('Error stack:', error.stack);
        console.error('Request body:', req.body);

        // If PQDI returns 500, provide helpful error message
        if (error.response && error.response.status === 500) {
            res.status(200).json({
                success: false,
                message: 'PQDI server is currently experiencing issues (HTTP 500). Please try again later or check https://www.pqdi.cc directly.',
                searchParams: req.body,
                pqdiError: true,
                results: { items: [], totalResults: 0, hasResults: false }
            });
        } else {
            res.status(500).json({
                success: false,
                message: 'Search failed: ' + error.message,
                searchParams: req.body,
                error: error.stack
            });
        }
    }
});

// Route to get detailed item information from tooltip API
app.get('/item-tooltip/:itemId', async (req, res) => {
    try {
        const itemId = req.params.itemId;
        console.log(`Fetching item details for ID: ${itemId}`);

        // Check cache first
        const cachedItem = await itemCache.getItem(itemId);
        if (cachedItem) {
            console.log(`Cache HIT for item ${itemId}`);
            res.json({
                success: true,
                itemId: itemId,
                details: cachedItem,
                fromCache: true
            });
            return;
        }

        console.log(`Cache MISS for item ${itemId}, fetching from PQDI...`);

        // Fetch tooltip data from PQDI
        const tooltipResponse = await axios.get(`https://www.pqdi.cc/get-item-tooltip/${itemId}`, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        // Parse the tooltip HTML to extract detailed item information
        const itemDetails = parseItemTooltip(tooltipResponse.data, itemId);

        // Fetch additional data from the main item page (drops and vendors)
        const pageData = await scrapeItemPageData(itemId);

        // Combine tooltip and page data
        const completeItemDetails = {
            ...itemDetails,
            ...pageData
        };

        // Save to cache
        await itemCache.saveItem(itemId, completeItemDetails);
        console.log(`Cached item ${itemId}: ${completeItemDetails.name} (drops: ${pageData.hasDrops}, purchasable: ${pageData.isPurchasable})`);

        res.json({
            success: true,
            itemId: itemId,
            details: itemDetails,
            fromCache: false
        });

    } catch (error) {
        console.error('Tooltip fetch error:', error.message);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch item details: ' + error.message,
            itemId: req.params.itemId
        });
    }
});

// Route to get multiple item details efficiently (for bulk operations)
app.post('/items-bulk', async (req, res) => {
    try {
        const { itemIds } = req.body;

        if (!itemIds || !Array.isArray(itemIds)) {
            return res.status(400).json({
                success: false,
                message: 'itemIds array is required'
            });
        }

        console.log(`Bulk fetch requested for ${itemIds.length} items`);

        // Get cached items first
        const cachedItems = await itemCache.getMultipleItems(itemIds);
        const cachedIds = Object.keys(cachedItems).map(id => parseInt(id));
        const missingIds = itemIds.filter(id => !cachedIds.includes(parseInt(id)));

        console.log(`Cache: ${cachedIds.length} hits, ${missingIds.length} misses`);

        const results = { ...cachedItems };
        let fetchedCount = 0;

        // Fetch missing items from PQDI with rate limiting
        for (const itemId of missingIds) {
            try {
                console.log(`Fetching item ${itemId} from PQDI... (${fetchedCount + 1}/${missingIds.length})`);

                // Rate limiting: wait between requests
                if (fetchedCount > 0) {
                    await new Promise(resolve => setTimeout(resolve, 10)); // 10ms delay
                }

                const tooltipResponse = await axios.get(`https://www.pqdi.cc/get-item-tooltip/${itemId}`, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    },
                    timeout: 10000 // 10 second timeout
                });

                const itemDetails = parseItemTooltip(tooltipResponse.data, itemId);

                // Fetch additional data from the main item page (drops and vendors)
                const pageData = await scrapeItemPageData(itemId);

                // Combine tooltip and page data
                const completeItemDetails = {
                    ...itemDetails,
                    ...pageData
                };

                // Save to cache
                await itemCache.saveItem(itemId, completeItemDetails);
                results[itemId] = completeItemDetails;

                fetchedCount++;
                console.log(`✓ Cached item ${itemId}: ${completeItemDetails.name} (drops: ${pageData.hasDrops}, purchasable: ${pageData.isPurchasable})`);

            } catch (error) {
                console.error(`Failed to fetch item ${itemId}:`, error.message);
                // Continue with other items even if one fails
            }
        }

        res.json({
            success: true,
            message: `Retrieved ${Object.keys(results).length} items (${cachedIds.length} from cache, ${fetchedCount} from PQDI)`,
            items: results,
            stats: {
                requested: itemIds.length,
                retrieved: Object.keys(results).length,
                fromCache: cachedIds.length,
                fromPQDI: fetchedCount,
                failed: missingIds.length - fetchedCount
            }
        });

    } catch (error) {
        console.error('Bulk fetch error:', error.message);
        res.status(500).json({
            success: false,
            message: 'Bulk fetch failed: ' + error.message
        });
    }
});


// Route to get cache statistics
app.get('/cache-stats', async (req, res) => {
    try {
        const stats = await itemCache.getCacheStats();
        res.json({
            success: true,
            stats: stats
        });
    } catch (error) {
        console.error('Cache stats error:', error.message);
        res.status(500).json({
            success: false,
            message: 'Failed to get cache stats: ' + error.message
        });
    }
});

// Route to clear all cache entries
app.post('/clear-cache', async (req, res) => {
    try {
        const deletedCount = await itemCache.clearAllEntries();
        console.log(`Cleared all cache entries: ${deletedCount} items deleted`);
        res.json({
            success: true,
            message: `Successfully cleared all cache entries (${deletedCount} items deleted)`,
            deletedCount: deletedCount
        });
    } catch (error) {
        console.error('Clear cache error:', error.message);
        res.status(500).json({
            success: false,
            message: 'Failed to clear cache: ' + error.message
        });
    }
});

// Function to scrape item page for drop and vendor information
async function scrapeItemPageData(itemId) {
    try {
        console.log(`Scraping item page data for item ${itemId}...`);

        const response = await axios.get(`https://www.pqdi.cc/item/${itemId}`, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Referer': 'https://www.pqdi.cc/items'
            }
        });

        const $ = cheerio.load(response.data);
        const dropsFrom = [];
        const purchasableFrom = [];

        // Parse "Drops From" tab - look for the actual tab content
        const dropsTabContent = $('#drops');
        if (dropsTabContent.length > 0) {
            let currentZone = '';

            dropsTabContent.find('li').each((i, element) => {
                const $li = $(element);
                const zoneLink = $li.find('a.large-text[href*="/zone/"]');
                const mobLink = $li.find('a.link[href*="/npc/"]');

                if (zoneLink.length > 0) {
                    // This is a zone entry
                    currentZone = zoneLink.text().trim();
                } else if (mobLink.length > 0 && currentZone) {
                    // This is a mob entry
                    const mobName = mobLink.text().trim();
                    const dropRateSpan = $li.find('span');
                    let dropRate = 'Unknown';

                    if (dropRateSpan.length > 0) {
                        const rateText = dropRateSpan.text().trim();
                        // Extract percentage from text like "(12.5% x 1)"
                        const match = rateText.match(/\(([0-9.]+)%/);
                        if (match) {
                            dropRate = match[1] + '%';
                        }
                    }

                    dropsFrom.push({
                        mob: mobName,
                        zone: currentZone,
                        dropRate: dropRate
                    });
                }
            });
        }

        // Alternative parsing for drops - look for any table with mob/zone data
        if (dropsFrom.length === 0) {
            $('table').each((i, table) => {
                const $table = $(table);
                const headers = $table.find('th').map((i, th) => $(th).text().toLowerCase()).get();

                if (headers.includes('mob') || headers.includes('npc') || headers.includes('creature')) {
                    $table.find('tr').slice(1).each((i, row) => {
                        const $row = $(row);
                        const cells = $row.find('td');

                        if (cells.length >= 2) {
                            const mobName = $(cells[0]).text().trim();
                            const zoneName = $(cells[1]).text().trim();
                            const dropRate = cells.length > 2 ? $(cells[2]).text().trim() : 'Unknown';

                            if (mobName && zoneName && mobName !== 'Mob' && zoneName !== 'Zone') {
                                dropsFrom.push({
                                    mob: mobName,
                                    zone: zoneName,
                                    dropRate: dropRate
                                });
                            }
                        }
                    });
                }
            });
        }

        // Parse "Purchased From" tab - look for the actual tab content
        const buyTabContent = $('#buy');
        if (buyTabContent.length > 0) {
            let currentZone = '';

            buyTabContent.find('li').each((i, element) => {
                const $li = $(element);
                const zoneLink = $li.find('a.large-text[href*="/zone/"]');
                const vendorLink = $li.find('a.link[href*="/npc/"]');

                if (zoneLink.length > 0) {
                    // This is a zone entry
                    currentZone = zoneLink.text().trim();
                } else if (vendorLink.length > 0 && currentZone) {
                    // This is a vendor entry
                    const vendorName = vendorLink.text().trim();
                    const priceSpan = $li.find('span');
                    let price = 'Unknown';

                    if (priceSpan.length > 0) {
                        price = priceSpan.text().trim();
                    }

                    purchasableFrom.push({
                        vendor: vendorName,
                        zone: currentZone,
                        price: price
                    });
                }
            });
        }

        // Alternative parsing for vendors
        if (purchasableFrom.length === 0) {
            $('table').each((i, table) => {
                const $table = $(table);
                const headers = $table.find('th').map((i, th) => $(th).text().toLowerCase()).get();

                if (headers.includes('vendor') || headers.includes('merchant') || headers.includes('npc')) {
                    $table.find('tr').slice(1).each((i, row) => {
                        const $row = $(row);
                        const cells = $row.find('td');

                        if (cells.length >= 2) {
                            const vendorName = $(cells[0]).text().trim();
                            const zoneName = $(cells[1]).text().trim();
                            const price = cells.length > 2 ? $(cells[2]).text().trim() : 'Unknown';

                            if (vendorName && zoneName && vendorName !== 'Vendor' && zoneName !== 'Zone') {
                                purchasableFrom.push({
                                    vendor: vendorName,
                                    zone: zoneName,
                                    price: price
                                });
                            }
                        }
                    });
                }
            });
        }

        console.log(`Item ${itemId}: Found ${dropsFrom.length} drop sources, ${purchasableFrom.length} vendors`);

        return {
            dropsFrom: dropsFrom,
            purchasableFrom: purchasableFrom,
            hasDrops: dropsFrom.length > 0,
            isPurchasable: purchasableFrom.length > 0
        };

    } catch (error) {
        console.error(`Error scraping item page for ${itemId}:`, error.message);
        return {
            dropsFrom: [],
            purchasableFrom: [],
            hasDrops: false,
            isPurchasable: false
        };
    }
}

// Function to parse item tooltip HTML and extract detailed information
function parseItemTooltip(html, itemId) {
    const $ = cheerio.load(html);
    const details = {
        itemId: itemId,
        name: '',
        isLore: false,
        isNoDrop: false,
        isMagic: false,
        slot: '',
        skill: '',
        size: '',
        weight: 0,
        baseDamage: 0,
        delay: 0,
        damageBonus: 0,
        ratio: 0,
        stats: {},
        classes: [],
        races: [],
        platinumValue: 0,
        goldValue: 0,
        silverValue: 0,
        copperValue: 0,
        rawHtml: html
    };

    // Extract item name
    const nameElement = $('h4').first();
    if (nameElement.length) {
        details.name = nameElement.text().trim();
    }

    // Extract item type flags (MAGIC ITEM LORE ITEM NODROP etc.)
    const itemTypeText = $('td:contains("MAGIC ITEM"), td:contains("LORE ITEM"), td:contains("NODROP")').text().toUpperCase();
    details.isLore = itemTypeText.includes('LORE ITEM');
    details.isNoDrop = itemTypeText.includes('NODROP');
    details.isMagic = itemTypeText.includes('MAGIC ITEM');

    // Extract slot information
    const slotElement = $('td:contains("Slot:")');
    if (slotElement.length) {
        const slotText = slotElement.text();
        const slotMatch = slotText.match(/Slot:\s*(.+)/);
        if (slotMatch) {
            details.slot = slotMatch[1].trim();
        }
    }

    // Extract skill information
    const skillElement = $('td:contains("Skill:")');
    if (skillElement.length) {
        const skillText = skillElement.text();
        const skillMatch = skillText.match(/Skill:\s*(.+)/);
        if (skillMatch) {
            details.skill = skillMatch[1].trim();
        }
    }

    // Extract size
    const sizeElement = $('td:contains("Size:")').next();
    if (sizeElement.length) {
        details.size = sizeElement.text().trim();
    }

    // Extract weight
    const weightElement = $('td:contains("Weight:")').next();
    if (weightElement.length) {
        details.weight = parseFloat(weightElement.text().trim()) || 0;
    }

    // Extract combat stats
    const baseDamageElement = $('td:contains("Base Damage:")').next();
    if (baseDamageElement.length) {
        details.baseDamage = parseInt(baseDamageElement.text().trim()) || 0;
    }

    const delayElement = $('td:contains("Delay:")').next();
    if (delayElement.length) {
        details.delay = parseInt(delayElement.text().trim()) || 0;
    }

    const damageBonusElement = $('td:contains("Damage bonus:")').next();
    if (damageBonusElement.length) {
        details.damageBonus = parseInt(damageBonusElement.text().trim()) || 0;
    }

    // Extract ratio
    const ratioElement = $('.text-info:contains("Ratio:")');
    if (ratioElement.length) {
        const ratioText = ratioElement.text();
        const ratioMatch = ratioText.match(/Ratio:\s*([\d.]+)/);
        if (ratioMatch) {
            details.ratio = parseFloat(ratioMatch[1]) || 0;
        }
    }

    // Extract character stats (STR, DEX, AGI, etc.)
    const statElements = $('td:contains("Strength:"), td:contains("Dexterity:"), td:contains("Agility:"), td:contains("Stamina:"), td:contains("Intelligence:"), td:contains("Wisdom:"), td:contains("Charisma:"), td:contains("Hit Points:"), td:contains("Mana:")');
    statElements.each((index, element) => {
        const $element = $(element);
        const statName = $element.text().replace(':', '').trim();
        const statValueElement = $element.next();
        if (statValueElement.length) {
            const statValue = parseInt(statValueElement.text().trim()) || 0;
            details.stats[statName.toLowerCase()] = statValue;
        }
    });

    // Extract classes
    const classesElement = $('td:contains("Classes:")');
    if (classesElement.length) {
        const classesText = classesElement.text();
        const classesMatch = classesText.match(/Classes:\s*(.+)/);
        if (classesMatch) {
            const classesStr = classesMatch[1].trim();
            if (classesStr !== 'ALL') {
                details.classes = classesStr.split(/\s+/).filter(cls => cls.length > 0);
            } else {
                details.classes = ['ALL'];
            }
        }
    }

    // Extract races
    const racesElement = $('td:contains("Races:")');
    if (racesElement.length) {
        const racesText = racesElement.text();
        const racesMatch = racesText.match(/Races:\s*(.+)/);
        if (racesMatch) {
            const racesStr = racesMatch[1].trim();
            if (racesStr !== 'ALL') {
                details.races = racesStr.split(/\s+/).filter(race => race.length > 0);
            } else {
                details.races = ['ALL'];
            }
        }
    }

    // Extract value (platinum, gold, silver, copper)
    const valueElement = $('td:contains("Value:")');
    if (valueElement.length) {
        const valueHTML = valueElement.html();
        // Look for numbers followed by coin images
        const platMatch = valueHTML.match(/(\d+)\s*<img[^>]*item_644/);
        const goldMatch = valueHTML.match(/(\d+)\s*<img[^>]*item_645/);
        const silverMatch = valueHTML.match(/(\d+)\s*<img[^>]*item_646/);
        const copperMatch = valueHTML.match(/(\d+)\s*<img[^>]*item_647/);

        details.platinumValue = platMatch ? parseInt(platMatch[1]) : 0;
        details.goldValue = goldMatch ? parseInt(goldMatch[1]) : 0;
        details.silverValue = silverMatch ? parseInt(silverMatch[1]) : 0;
        details.copperValue = copperMatch ? parseInt(copperMatch[1]) : 0;
    }

    return details;
}

// Function to parse search results from PQDI HTML response
function parseSearchResults(html) {
    const $ = cheerio.load(html);
    const items = [];
    let totalResults = 0;
    let currentPage = 1;
    let totalPages = 1;

    // Find all item links
    const allLinks = $('a[href*="/item/"]');

    // Check if there are results
    const resultText = $('body').text();
    if (resultText.includes('No items found') || resultText.includes('no items found')) {
        return {
            items: [],
            totalResults: 0,
            currentPage: 1,
            totalPages: 1,
            hasResults: false
        };
    }

    // Parse pagination info if available
    const paginationText = $('.pagination').text();
    if (paginationText) {
        const pageMatch = paginationText.match(/Page (\d+) of (\d+)/);
        if (pageMatch) {
            currentPage = parseInt(pageMatch[1]);
            totalPages = parseInt(pageMatch[2]);
        }
    }

    // Parse item results - look for any element containing item links
    $('a[href*="/item/"]').each((index, element) => {
        const $link = $(element);
        const $parent = $link.closest('tr, .card, .item, div');

        const item = {};

        // Extract item name and link
        item.name = $link.text().trim();
        item.link = 'https://www.pqdi.cc' + $link.attr('href');
        item.id = $link.attr('href').match(/\/item\/(\d+)/)?.[1];

        // Extract item stats from text content
        const itemText = $parent.text();

        // Try to extract AC
        const acMatch = itemText.match(/AC:\s*(\d+)/i);
        if (acMatch) item.ac = parseInt(acMatch[1]);

        // Try to extract HP
        const hpMatch = itemText.match(/HP:\s*([+-]?\d+)/i);
        if (hpMatch) item.hp = parseInt(hpMatch[1]);

        // Try to extract Mana
        const manaMatch = itemText.match(/MANA:\s*([+-]?\d+)/i);
        if (manaMatch) item.mana = parseInt(manaMatch[1]);

        // Try to extract stats (STR, DEX, etc.)
        const statMatches = itemText.match(/(STR|DEX|AGI|STA|INT|WIS|CHA):\s*([+-]?\d+)/gi);
        if (statMatches) {
            statMatches.forEach(match => {
                const [stat, value] = match.split(':');
                item[stat.toLowerCase().trim()] = parseInt(value.trim());
            });
        }

        // Try to extract damage/delay for weapons
        const damageMatch = itemText.match(/(\d+)\/(\d+)/);
        if (damageMatch) {
            item.damage = parseInt(damageMatch[1]);
            item.delay = parseInt(damageMatch[2]);
        }

        // Extract item type/slot info
        const slotMatch = itemText.match(/(PRIMARY|SECONDARY|CHEST|LEGS|ARMS|HEAD|FACE|NECK|SHOULDERS|BACK|WAIST|WRISTS|HANDS|FINGERS|FEET|EARS|RANGE|AMMO)/i);
        if (slotMatch) item.slot = slotMatch[1];

        // Extract class restrictions
        const classMatch = itemText.match(/(WAR|CLR|PAL|RNG|SHD|DRU|MNK|BRD|ROG|SHM|NEC|WIZ|MAG|ENC|BST)/g);
        if (classMatch) item.classes = classMatch;

        // Extract race restrictions
        const raceMatch = itemText.match(/(HUM|BAR|ERU|ELF|HIE|DEF|HEF|DWF|TRL|OGR|HFL|GNM|IKS|VAH)/g);
        if (raceMatch) item.races = raceMatch;

        // Only add if we found a name
        if (item.name) {
            items.push(item);
        }
    });

    // Try to get total results count
    const resultsCountText = $('.container').text();
    const countMatch = resultsCountText.match(/(\d+)\s+items?\s+found/i);
    if (countMatch) {
        totalResults = parseInt(countMatch[1]);
    } else {
        totalResults = items.length;
    }

    return {
        items: items,
        totalResults: totalResults,
        currentPage: currentPage,
        totalPages: totalPages,
        hasResults: items.length > 0
    };
}

const server = app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log('Item caching system initialized');
    console.log('Server is ready to accept connections');
});

server.on('error', (err) => {
    console.error('Server error:', err);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down gracefully...');
    itemCache.close();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\nShutting down gracefully...');
    itemCache.close();
    process.exit(0);
});