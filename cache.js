const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class ItemCache {
    constructor() {
        this.db = null;
        this.dbPath = path.join(__dirname, 'item_cache.db');
        this.initDatabase();
    }

    initDatabase() {
        this.db = new sqlite3.Database(this.dbPath, (err) => {
            if (err) {
                console.error('Error opening database:', err.message);
            } else {
                console.log('Connected to SQLite database for item caching');
                this.createTables();
            }
        });
    }

    createTables() {
        // First check if we need to migrate the schema
        this.db.all("PRAGMA table_info(items)", (err, columns) => {
            if (err) {
                console.error('Error checking table schema:', err);
                return;
            }

            const columnNames = columns.map(col => col.name);
            const needsMigration = !columnNames.includes('dropsFrom') || !columnNames.includes('purchasableFrom');

            if (needsMigration && columns.length > 0) {
                console.log('Migrating database schema...');
                this.migrateSchema();
            } else {
                this.createItemsTable();
            }
        });
    }

    migrateSchema() {
        // Add new columns to existing table
        const migrations = [
            'ALTER TABLE items ADD COLUMN dropsFrom TEXT DEFAULT "[]"',
            'ALTER TABLE items ADD COLUMN purchasableFrom TEXT DEFAULT "[]"',
            'ALTER TABLE items ADD COLUMN hasDrops BOOLEAN DEFAULT 0',
            'ALTER TABLE items ADD COLUMN isPurchasable BOOLEAN DEFAULT 0'
        ];

        let completed = 0;
        migrations.forEach((migration, index) => {
            this.db.run(migration, (err) => {
                if (err && !err.message.includes('duplicate column name')) {
                    console.error(`Migration ${index + 1} failed:`, err.message);
                } else {
                    console.log(`Migration ${index + 1} completed`);
                }

                completed++;
                if (completed === migrations.length) {
                    console.log('Database migration completed');
                    this.createIndexes();
                }
            });
        });
    }

    createItemsTable() {
        const createItemsTable = `
            CREATE TABLE IF NOT EXISTS items (
                id INTEGER PRIMARY KEY,
                name TEXT,
                isLore BOOLEAN,
                isNoDrop BOOLEAN,
                isMagic BOOLEAN,
                slot TEXT,
                skill TEXT,
                size TEXT,
                weight REAL,
                baseDamage INTEGER,
                delay INTEGER,
                damageBonus INTEGER,
                ratio REAL,
                stats TEXT,
                classes TEXT,
                races TEXT,
                platinumValue INTEGER,
                goldValue INTEGER,
                silverValue INTEGER,
                copperValue INTEGER,
                rawHtml TEXT,
                dropsFrom TEXT,
                purchasableFrom TEXT,
                hasDrops BOOLEAN DEFAULT 0,
                isPurchasable BOOLEAN DEFAULT 0,
                lastUpdated DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `;

        this.db.run(createItemsTable, (err) => {
            if (err) {
                console.error('Error creating items table:', err.message);
            } else {
                console.log('Items table ready');
                this.createIndexes();
            }
        });
    }

    createIndexes() {
        const createIndexes = `
            CREATE INDEX IF NOT EXISTS idx_items_id ON items(id);
            CREATE INDEX IF NOT EXISTS idx_items_name ON items(name);
            CREATE INDEX IF NOT EXISTS idx_items_lastUpdated ON items(lastUpdated);
            CREATE INDEX IF NOT EXISTS idx_items_hasDrops ON items(hasDrops);
            CREATE INDEX IF NOT EXISTS idx_items_isPurchasable ON items(isPurchasable);
        `;

        this.db.exec(createIndexes, (err) => {
            if (err) {
                console.error('Error creating indexes:', err.message);
            } else {
                console.log('Database indexes ready');
            }
        });
    }

    async getItem(itemId) {
        return new Promise((resolve, reject) => {
            const query = 'SELECT * FROM items WHERE id = ?';
            this.db.get(query, [itemId], (err, row) => {
                if (err) {
                    reject(err);
                } else if (row) {
                    // Parse JSON fields back to objects
                    const item = {
                        ...row,
                        stats: row.stats ? JSON.parse(row.stats) : {},
                        classes: row.classes ? JSON.parse(row.classes) : [],
                        races: row.races ? JSON.parse(row.races) : [],
                        dropsFrom: row.dropsFrom ? JSON.parse(row.dropsFrom) : [],
                        purchasableFrom: row.purchasableFrom ? JSON.parse(row.purchasableFrom) : []
                    };
                    resolve(item);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async saveItem(itemId, itemDetails) {
        return new Promise((resolve, reject) => {
            const query = `
                INSERT OR REPLACE INTO items (
                    id, name, isLore, isNoDrop, isMagic, slot, skill, size, weight,
                    baseDamage, delay, damageBonus, ratio, stats, classes, races,
                    platinumValue, goldValue, silverValue, copperValue, rawHtml,
                    dropsFrom, purchasableFrom, hasDrops, isPurchasable, lastUpdated
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `;

            const params = [
                itemId,
                itemDetails.name || '',
                itemDetails.isLore || false,
                itemDetails.isNoDrop || false,
                itemDetails.isMagic || false,
                itemDetails.slot || '',
                itemDetails.skill || '',
                itemDetails.size || '',
                itemDetails.weight || 0,
                itemDetails.baseDamage || 0,
                itemDetails.delay || 0,
                itemDetails.damageBonus || 0,
                itemDetails.ratio || 0,
                JSON.stringify(itemDetails.stats || {}),
                JSON.stringify(itemDetails.classes || []),
                JSON.stringify(itemDetails.races || []),
                itemDetails.platinumValue || 0,
                itemDetails.goldValue || 0,
                itemDetails.silverValue || 0,
                itemDetails.copperValue || 0,
                itemDetails.rawHtml || '',
                JSON.stringify(itemDetails.dropsFrom || []),
                JSON.stringify(itemDetails.purchasableFrom || []),
                itemDetails.hasDrops || false,
                itemDetails.isPurchasable || false
            ];

            this.db.run(query, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve(this.changes);
                }
            });
        });
    }

    async getCacheStats() {
        return new Promise((resolve, reject) => {
            const query = `
                SELECT 
                    COUNT(*) as totalItems,
                    MIN(lastUpdated) as oldestEntry,
                    MAX(lastUpdated) as newestEntry
                FROM items
            `;
            this.db.get(query, [], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    async clearOldEntries(daysOld = 30) {
        return new Promise((resolve, reject) => {
            const query = 'DELETE FROM items WHERE lastUpdated < datetime("now", "-" || ? || " days")';
            this.db.run(query, [daysOld], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve(this.changes);
                }
            });
        });
    }

    async clearAllEntries() {
        return new Promise((resolve, reject) => {
            const query = 'DELETE FROM items';
            this.db.run(query, [], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve(this.changes);
                }
            });
        });
    }

    async getMultipleItems(itemIds) {
        if (!itemIds || itemIds.length === 0) {
            return {};
        }

        return new Promise((resolve, reject) => {
            const placeholders = itemIds.map(() => '?').join(',');
            const query = `SELECT * FROM items WHERE id IN (${placeholders})`;
            
            this.db.all(query, itemIds, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    const items = {};
                    rows.forEach(row => {
                        items[row.id] = {
                            ...row,
                            stats: row.stats ? JSON.parse(row.stats) : {},
                            classes: row.classes ? JSON.parse(row.classes) : [],
                            races: row.races ? JSON.parse(row.races) : [],
                            dropsFrom: row.dropsFrom ? JSON.parse(row.dropsFrom) : [],
                            purchasableFrom: row.purchasableFrom ? JSON.parse(row.purchasableFrom) : []
                        };
                    });
                    resolve(items);
                }
            });
        });
    }

    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('Error closing database:', err.message);
                } else {
                    console.log('Database connection closed');
                }
            });
        }
    }
}

module.exports = ItemCache;
