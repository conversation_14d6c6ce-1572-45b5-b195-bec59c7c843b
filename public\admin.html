<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQDI Cache Admin</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-card {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 10px;
            background: var(--bg-secondary);
            border-radius: 5px;
        }
        
        .stat-label {
            font-weight: bold;
            color: var(--accent-primary);
        }
        
        .stat-value {
            color: var(--text-primary);
        }
        
        .refresh-btn {
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        
        .refresh-btn:hover {
            background: var(--accent-secondary);
        }
        
        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--accent-primary);
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <header>
            <h1>PQDI Cache Administration</h1>
            <p>Monitor and manage the item cache system</p>
        </header>

        <div class="stats-card">
            <h3>Cache Statistics</h3>
            <button id="refreshBtn" class="refresh-btn">Refresh Stats</button>
            
            <div id="statsContent">
                <div class="stat-item">
                    <span class="stat-label">Total Cached Items:</span>
                    <span class="stat-value" id="totalItems">Loading...</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Oldest Entry:</span>
                    <span class="stat-value" id="oldestEntry">Loading...</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Newest Entry:</span>
                    <span class="stat-value" id="newestEntry">Loading...</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Cache Hit Rate:</span>
                    <span class="stat-value" id="hitRate">Not available</span>
                </div>
            </div>
        </div>

        <div class="stats-card">
            <h3>Actions</h3>
            <p>Administrative actions for cache management</p>

            <button id="testBulkBtn" class="refresh-btn">Test Bulk Fetch (10 items)</button>
            <button id="clearOldBtn" class="refresh-btn">Clear Old Entries (30+ days)</button>
            <button id="clearAllBtn" class="refresh-btn" style="background-color: #dc3545; margin-left: 10px;">Clear All Cache</button>

            <div id="actionResults" style="margin-top: 20px;"></div>
        </div>

        <div class="stats-card">
            <h3>Navigation</h3>
            <a href="/" style="color: var(--accent-primary);">← Back to Item Search</a>
        </div>
    </div>

    <script>
        let cacheStats = {};
        
        async function loadCacheStats() {
            try {
                document.getElementById('refreshBtn').innerHTML = '<span class="loading"></span> Loading...';
                
                const response = await fetch('/cache-stats');
                const result = await response.json();
                
                if (result.success) {
                    cacheStats = result.stats;
                    updateStatsDisplay();
                } else {
                    console.error('Failed to load cache stats:', result.message);
                }
            } catch (error) {
                console.error('Error loading cache stats:', error);
            } finally {
                document.getElementById('refreshBtn').innerHTML = 'Refresh Stats';
            }
        }
        
        function updateStatsDisplay() {
            document.getElementById('totalItems').textContent = cacheStats.totalItems || 0;
            document.getElementById('oldestEntry').textContent = cacheStats.oldestEntry || 'None';
            document.getElementById('newestEntry').textContent = cacheStats.newestEntry || 'None';
        }
        
        async function testBulkFetch() {
            try {
                document.getElementById('testBulkBtn').innerHTML = '<span class="loading"></span> Testing...';

                // Test with some common item IDs
                const testIds = ['5001', '5002', '5003', '5004', '5005', '5008', '5013', '5016', '5019', '5022'];

                const response = await fetch('/items-bulk', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ itemIds: testIds })
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('actionResults').innerHTML = `
                        <div style="background: var(--bg-secondary); padding: 15px; border-radius: 5px; border: 1px solid var(--border-color);">
                            <h4>Bulk Fetch Test Results:</h4>
                            <p><strong>Message:</strong> ${result.message}</p>
                            <p><strong>Requested:</strong> ${result.stats.requested} items</p>
                            <p><strong>Retrieved:</strong> ${result.stats.retrieved} items</p>
                            <p><strong>From Cache:</strong> ${result.stats.fromCache} items</p>
                            <p><strong>From PQDI:</strong> ${result.stats.fromPQDI} items</p>
                            <p><strong>Failed:</strong> ${result.stats.failed} items</p>
                        </div>
                    `;

                    // Refresh stats after bulk fetch
                    setTimeout(loadCacheStats, 1000);
                } else {
                    document.getElementById('actionResults').innerHTML = `
                        <div style="background: rgba(244, 67, 54, 0.1); padding: 15px; border-radius: 5px; border: 1px solid var(--error-color);">
                            <h4>Error:</h4>
                            <p>${result.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error in bulk fetch test:', error);
                document.getElementById('actionResults').innerHTML = `
                    <div style="background: rgba(244, 67, 54, 0.1); padding: 15px; border-radius: 5px; border: 1px solid var(--error-color);">
                        <h4>Error:</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                document.getElementById('testBulkBtn').innerHTML = 'Test Bulk Fetch (10 items)';
            }
        }

        async function clearAllCache() {
            // Confirm action since this is destructive
            if (!confirm('Are you sure you want to clear ALL cached items? This action cannot be undone.')) {
                return;
            }

            try {
                document.getElementById('clearAllBtn').innerHTML = '<span class="loading"></span> Clearing...';

                const response = await fetch('/clear-cache', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('actionResults').innerHTML = `
                        <div style="background: var(--bg-secondary); padding: 15px; border-radius: 5px; border: 1px solid var(--border-color);">
                            <h4>Cache Cleared Successfully:</h4>
                            <p><strong>Message:</strong> ${result.message}</p>
                            <p><strong>Items Deleted:</strong> ${result.deletedCount}</p>
                        </div>
                    `;

                    // Refresh stats after clearing cache
                    setTimeout(loadCacheStats, 1000);
                } else {
                    document.getElementById('actionResults').innerHTML = `
                        <div style="background: rgba(244, 67, 54, 0.1); padding: 15px; border-radius: 5px; border: 1px solid var(--error-color);">
                            <h4>Error:</h4>
                            <p>${result.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error clearing cache:', error);
                document.getElementById('actionResults').innerHTML = `
                    <div style="background: rgba(244, 67, 54, 0.1); padding: 15px; border-radius: 5px; border: 1px solid var(--error-color);">
                        <h4>Error:</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                document.getElementById('clearAllBtn').innerHTML = 'Clear All Cache';
            }
        }
        
        // Event listeners
        document.getElementById('refreshBtn').addEventListener('click', loadCacheStats);
        document.getElementById('testBulkBtn').addEventListener('click', testBulkFetch);
        document.getElementById('clearAllBtn').addEventListener('click', clearAllCache);

        // Load initial stats
        loadCacheStats();
    </script>
</body>
</html>
